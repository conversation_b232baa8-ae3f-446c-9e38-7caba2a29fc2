import os
import pandas as pd
import datetime
from dhanhq import dhanhq

client_id = "1105577608"  # Replace with your Dhan client code (string)
access_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"     # Replace with your Dhan token ID (string)


dhan = dhanhq(client_id, access_token)

# Stocks to fetch
stocks = [
    {'name': 'RELIANCE', 'security_id': '1333', 'exchange_segment': 'NSE_EQ', 'instrument_type': 'EQUITY'},
    {'name': 'TCS', 'security_id': '11536', 'exchange_segment': 'NSE_EQ', 'instrument_type': 'EQUITY'},
    {'name': 'INFY', 'security_id': '1594', 'exchange_segment': 'NSE_EQ', 'instrument_type': 'EQUITY'},
    {'name': 'HDFCBANK', 'security_id': '1330', 'exchange_segment': 'NSE_EQ', 'instrument_type': 'EQUITY'},
    {'name': 'ICICIBANK', 'security_id': '1334', 'exchange_segment': 'NSE_EQ', 'instrument_type': 'EQUITY'},
    {'name': 'NIFTY 50', 'security_id': '*********', 'exchange_segment': 'NSE_IDX', 'instrument_type': 'INDEX'}
]

timeframes = ['1', '5', '15', '25', '60']
today = datetime.datetime.now().date()
now = datetime.datetime.now()
market_open = datetime.time(9, 15)
market_close = datetime.time(15, 30)

# Directories
os.makedirs('stock_data', exist_ok=True)
os.makedirs('logs', exist_ok=True)

# Logging function
def log_event(stock, timeframe, event_type, message):
    log_file = 'logs/activity_log.csv'
    timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    entry = pd.DataFrame([[timestamp, stock, timeframe, event_type, message]],
                         columns=['timestamp', 'stock', 'timeframe', 'event_type', 'message'])
    if os.path.exists(log_file):
        entry.to_csv(log_file, mode='a', header=False, index=False)
    else:
        entry.to_csv(log_file, mode='w', header=True, index=False)

# File path helpers
def intraday_filename(stock, interval):
    return f"stock_data/{stock['name']}intraday{interval}min.csv"

def daily_filename(stock):
    return f"stock_data/{stock['name']}_daily.csv"

def is_trading_day(date):
    return date.weekday() < 5  # Mon-Fri only

# API fetch functions
def fetch_intraday_data(stock, interval, from_dt, to_dt):
    try:
        data = dhan.intraday_minute_data(
            security_id=stock['security_id'],
            exchange_segment=stock['exchange_segment'],
            instrument_type=stock['instrument_type'],
            interval=interval,
            from_date=from_dt.strftime('%Y-%m-%d %H:%M:%S'),
            to_date=to_dt.strftime('%Y-%m-%d %H:%M:%S')
        )
        if data['status'] == 'success':
            df = pd.DataFrame(data['data'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
            return df
        else:
            log_event(stock['name'], f"{interval}min", 'API_ERROR', data.get('message', 'No message'))
    except Exception as e:
        log_event(stock['name'], f"{interval}min", 'EXCEPTION', str(e))
    return pd.DataFrame()

def fetch_daily_data(stock, from_date, to_date):
    try:
        data = dhan.historical_daily_data(
            security_id=stock['security_id'],
            exchange_segment=stock['exchange_segment'],
            instrument_type=stock['instrument_type'],
            expiry_code=0,
            from_date=from_date.strftime('%Y-%m-%d'),
            to_date=to_date.strftime('%Y-%m-%d')
        )
        if data['status'] == 'success':
            df = pd.DataFrame(data['data'])
            # Rename timestamp to startTime for consistency with the rest of the script
            if 'timestamp' in df.columns:
                df['startTime'] = pd.to_datetime(df['timestamp'], unit='s')
                df.drop('timestamp', axis=1, inplace=True)
            return df
        else:
            log_event(stock['name'], 'daily', 'API_ERROR', data.get('message', 'No message'))
    except Exception as e:
        log_event(stock['name'], 'daily', 'EXCEPTION', str(e))
    return pd.DataFrame()

# Update logic
def update_intraday_data(stock):
    for interval in timeframes:
        file = intraday_filename(stock, interval)
        from_dt = datetime.datetime.combine(today, market_open)
        to_dt = now

        if os.path.exists(file):
            existing = pd.read_csv(file)
            existing['timestamp'] = pd.to_datetime(existing['timestamp'])
            latest_time = existing['timestamp'].max()
            if latest_time.date() >= today:
                log_event(stock['name'], f"{interval}min", 'SKIP', 'Intraday data already up-to-date')
                continue
            from_dt = latest_time + datetime.timedelta(minutes=int(interval))

        df_new = fetch_intraday_data(stock, interval, from_dt, to_dt)
        if not df_new.empty:
            if os.path.exists(file):
                df_existing = pd.read_csv(file)
                df_combined = pd.concat([df_existing, df_new], ignore_index=True)
            else:
                df_combined = df_new
            df_combined.drop_duplicates(subset='timestamp', keep='last', inplace=True)
            df_combined.to_csv(file, index=False)
            log_event(stock['name'], f"{interval}min", 'UPDATE', f"Intraday data updated with {len(df_new)} new rows")

def update_daily_data(stock):
    file = daily_filename(stock)
    last_date = today - datetime.timedelta(days=1)

    if os.path.exists(file):
        existing = pd.read_csv(file)
        existing['startTime'] = pd.to_datetime(existing['startTime'])
        last_available = existing['startTime'].max().date()
        if last_available >= last_date:
            log_event(stock['name'], 'daily', 'SKIP', 'Daily data already up-to-date')
            return
        from_date = last_available + datetime.timedelta(days=1)
    else:
        from_date = last_date - datetime.timedelta(days=3)

    df_new = fetch_daily_data(stock, from_date, last_date)
    if not df_new.empty:
        if os.path.exists(file):
            df_existing = pd.read_csv(file)
            df_combined = pd.concat([df_existing, df_new], ignore_index=True)
        else:
            df_combined = df_new

        df_combined['startTime'] = pd.to_datetime(df_combined['startTime'])
        df_combined.drop_duplicates(subset='startTime', keep='last', inplace=True)
        df_combined.sort_values('startTime', inplace=True)
        df_combined = df_combined.tail(3)  # Keep only last 3 days
        df_combined.to_csv(file, index=False)
        log_event(stock['name'], 'daily', 'UPDATE', f"Daily data updated with {len(df_new)} new rows")

# Main execution
if is_trading_day(today):
    if market_open <= now.time() <= market_close:
        for stock in stocks:
            update_intraday_data(stock)
    else:
        for stock in stocks:
            update_daily_data(stock)
else:
    for stock in stocks:
        log_event(stock['name'], 'all', 'SKIP', 'Market closed today')
