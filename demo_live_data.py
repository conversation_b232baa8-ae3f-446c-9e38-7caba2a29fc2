#!/usr/bin/env python3
"""
Demo script to showcase live data fetching capabilities
"""

import datetime
import time
import pandas as pd
from dhanhq import dhanhq

# Configuration
client_id = "1105577608"
access_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

# Initialize Dhan client
dhan = dhanhq(client_id, access_token)

# Sample stocks for demo
demo_stocks = [
    {'name': 'RELIANCE', 'security_id': '1333', 'exchange_segment': 'NSE_EQ'},
    {'name': 'TCS', 'security_id': '11536', 'exchange_segment': 'NSE_EQ'},
    {'name': 'INFY', 'security_id': '1594', 'exchange_segment': 'NSE_EQ'}
]

def demo_market_quote():
    """Demonstrate Market Quote API"""
    print("🔴 DEMO: Market Quote API")
    print("-" * 40)
    
    # Prepare securities for Market Quote API
    securities = {}
    for stock in demo_stocks:
        exchange = stock['exchange_segment']
        if exchange not in securities:
            securities[exchange] = []
        securities[exchange].append(stock['security_id'])
    
    try:
        # Fetch live quotes
        response = dhan.ohlc_data(securities)
        
        if response.get('status') == 'success':
            print("✅ Successfully fetched live market quotes:")
            data = response['data']
            
            # Create a nice table display
            quote_data = []
            for exchange, instruments in data.items():
                for security_id, quote in instruments.items():
                    stock_name = next((s['name'] for s in demo_stocks if s['security_id'] == security_id), security_id)
                    quote_data.append({
                        'Stock': stock_name,
                        'LTP': quote.get('LTP', 'N/A'),
                        'Open': quote.get('open', 'N/A'),
                        'High': quote.get('high', 'N/A'),
                        'Low': quote.get('low', 'N/A'),
                        'Volume': quote.get('volume', 'N/A')
                    })
            
            if quote_data:
                df = pd.DataFrame(quote_data)
                print(df.to_string(index=False))
            else:
                print("No quote data available")
                
        else:
            print(f"❌ Failed to fetch quotes: {response.get('message', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

def demo_historical_data():
    """Demonstrate Historical Data API"""
    print("\n🔴 DEMO: Historical Data API")
    print("-" * 40)
    
    # Test with RELIANCE
    test_stock = demo_stocks[0]
    
    try:
        # Fetch recent daily data
        to_date = datetime.date.today() - datetime.timedelta(days=1)
        from_date = to_date - datetime.timedelta(days=2)
        
        print(f"Fetching daily data for {test_stock['name']} from {from_date} to {to_date}")
        
        response = dhan.historical_daily_data(
            security_id=test_stock['security_id'],
            exchange_segment=test_stock['exchange_segment'],
            instrument_type='EQUITY',
            expiry_code=0,
            from_date=from_date.strftime('%Y-%m-%d'),
            to_date=to_date.strftime('%Y-%m-%d')
        )
        
        if response.get('status') == 'success':
            print("✅ Successfully fetched historical data:")
            data = response['data']
            
            # Convert to DataFrame for better display
            df = pd.DataFrame(data)
            if not df.empty and 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
                df['date'] = df['timestamp'].dt.date
                
                # Display key columns
                display_cols = ['date', 'open', 'high', 'low', 'close', 'volume']
                available_cols = [col for col in display_cols if col in df.columns]
                print(df[available_cols].to_string(index=False))
            else:
                print("No historical data available or unexpected format")
                
        else:
            print(f"❌ Failed to fetch historical data: {response.get('message', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

def demo_intraday_data():
    """Demonstrate Intraday Data API"""
    print("\n🔴 DEMO: Intraday Data API")
    print("-" * 40)
    
    # Test with RELIANCE
    test_stock = demo_stocks[0]
    
    try:
        # Fetch recent intraday data (yesterday's data)
        yesterday = datetime.date.today() - datetime.timedelta(days=1)
        from_dt = datetime.datetime.combine(yesterday, datetime.time(9, 15))
        to_dt = datetime.datetime.combine(yesterday, datetime.time(10, 0))
        
        print(f"Fetching 5-minute intraday data for {test_stock['name']}")
        print(f"From: {from_dt} To: {to_dt}")
        
        response = dhan.intraday_minute_data(
            security_id=test_stock['security_id'],
            exchange_segment=test_stock['exchange_segment'],
            instrument_type='EQUITY',
            interval=5,  # Use integer instead of string
            from_date=from_dt.strftime('%Y-%m-%d %H:%M:%S'),
            to_date=to_dt.strftime('%Y-%m-%d %H:%M:%S')
        )
        
        if response.get('status') == 'success':
            print("✅ Successfully fetched intraday data:")
            data = response['data']
            
            # Convert to DataFrame for better display
            df = pd.DataFrame(data)
            if not df.empty and 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
                df['time'] = df['timestamp'].dt.strftime('%H:%M')
                
                # Display key columns
                display_cols = ['time', 'open', 'high', 'low', 'close', 'volume']
                available_cols = [col for col in display_cols if col in df.columns]
                print(df[available_cols].to_string(index=False))
            else:
                print("No intraday data available or unexpected format")
                
        else:
            print(f"❌ Failed to fetch intraday data: {response.get('message', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

def demo_live_monitoring():
    """Demonstrate live monitoring capabilities"""
    print("\n🔴 DEMO: Live Monitoring (5 iterations)")
    print("-" * 40)
    
    for i in range(5):
        current_time = datetime.datetime.now().strftime('%H:%M:%S')
        print(f"\n📊 Iteration {i+1}/5 at {current_time}")
        
        # Fetch live quotes
        securities = {"NSE_EQ": ["1333"]}  # RELIANCE only for demo
        
        try:
            response = dhan.ohlc_data(securities)
            
            if response.get('status') == 'success':
                data = response['data']
                if 'NSE_EQ' in data and '1333' in data['NSE_EQ']:
                    quote = data['NSE_EQ']['1333']
                    print(f"RELIANCE - LTP: {quote.get('LTP', 'N/A')}, Volume: {quote.get('volume', 'N/A')}")
                else:
                    print("No data available")
            else:
                print(f"API Error: {response.get('message', 'Unknown error')}")
                
        except Exception as e:
            print(f"Exception: {str(e)}")
        
        if i < 4:  # Don't sleep after last iteration
            print("Waiting 3 seconds...")
            time.sleep(3)

def main():
    """Run all demos"""
    print("🚀 DhanHQ Live Data API Demo")
    print("=" * 50)
    print(f"Timestamp: {datetime.datetime.now()}")
    print(f"Demo stocks: {', '.join([s['name'] for s in demo_stocks])}")
    print("=" * 50)
    
    # Run demos
    demo_market_quote()
    demo_historical_data()
    demo_intraday_data()
    demo_live_monitoring()
    
    print("\n" + "=" * 50)
    print("🎉 Demo completed!")
    print("\nKey Features Demonstrated:")
    print("✅ Market Quote API - Real-time OHLC data")
    print("✅ Historical Daily Data API - Past daily candles")
    print("✅ Intraday Data API - Minute-level historical data")
    print("✅ Live Monitoring - Continuous data fetching")
    print("\nThe enhanced TestDHanAPI.py script includes:")
    print("• Automatic gap detection and validation")
    print("• Retry logic with rate limiting")
    print("• Live data collection during market hours")
    print("• Comprehensive logging and error handling")

if __name__ == "__main__":
    main()
