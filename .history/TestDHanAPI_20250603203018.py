#!/usr/bin/env python3
"""
Enhanced DhanHQ Live Data Fetcher
- Continuous operation with market hours detection
- 5 trading days of historical data management
- Live data collection during market hours
- Comprehensive timeframe support (1min, 5min, 15min, 25min, 60min, daily)
- Rolling window data management
"""

import os
import pandas as pd
import datetime
import time
import json
from dhanhq import dhanhq

# Configuration
CLIENT_ID = "1105577608"
ACCESS_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

# API Configuration - Optimized for DhanHQ v2.2
API_RATE_LIMIT_DELAY = 0.05  # Minimal delay since no rate limits on minute timeframes
MAX_RETRIES = 2
RETRY_DELAY = 0.5

# Data Management Configuration
HISTORICAL_DAYS = 5  # Keep last 5 trading days of intraday data
DAILY_DATA_DAYS = 3  # Keep last 3 days of daily data (rolling window)
LIVE_DATA_INTERVAL = 30  # Fetch live quotes every 30 seconds
INTRADAY_UPDATE_INTERVAL = 60  # Update intraday data every 60 seconds

# Market Timing
MARKET_OPEN = datetime.time(9, 15)
MARKET_CLOSE = datetime.time(15, 30)
PRE_MARKET_START = datetime.time(9, 0)

# Initialize Dhan client
dhan = dhanhq(CLIENT_ID, ACCESS_TOKEN)

# Stock Configuration - 5 different stocks + NIFTY 50
STOCKS = [
    {'name': 'RELIANCE', 'security_id': '1333', 'exchange_segment': 'NSE_EQ', 'instrument_type': 'EQUITY'},
    {'name': 'TCS', 'security_id': '11536', 'exchange_segment': 'NSE_EQ', 'instrument_type': 'EQUITY'},
    {'name': 'INFY', 'security_id': '1594', 'exchange_segment': 'NSE_EQ', 'instrument_type': 'EQUITY'},
    {'name': 'HDFCBANK', 'security_id': '1330', 'exchange_segment': 'NSE_EQ', 'instrument_type': 'EQUITY'},
    {'name': 'ICICIBANK', 'security_id': '1334', 'exchange_segment': 'NSE_EQ', 'instrument_type': 'EQUITY'},
    {'name': 'NIFTY50', 'security_id': '13', 'exchange_segment': 'NSE_IDX', 'instrument_type': 'INDEX'}
]

# Timeframe Configuration
TIMEFRAMES = {
    '1min': 1,
    '5min': 5,
    '15min': 15,
    '25min': 25,
    '60min': 60,
    'daily': 'daily'
}

# Directories
os.makedirs('stock_data', exist_ok=True)
os.makedirs('logs', exist_ok=True)

# Logging function
def log_event(stock, timeframe, event_type, message):
    log_file = 'logs/activity_log.csv'
    timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    entry = pd.DataFrame([[timestamp, stock, timeframe, event_type, message]],
                         columns=['timestamp', 'stock', 'timeframe', 'event_type', 'message'])
    if os.path.exists(log_file):
        entry.to_csv(log_file, mode='a', header=False, index=False)
    else:
        entry.to_csv(log_file, mode='w', header=True, index=False)

# File path helpers
def intraday_filename(stock, interval):
    return f"stock_data/{stock['name']}intraday{interval}min.csv"

def daily_filename(stock):
    return f"stock_data/{stock['name']}_daily.csv"

def is_trading_day(date):
    return date.weekday() < 5  # Mon-Fri only

# Enhanced API fetch functions with retry logic and rate limiting
def api_call_with_retry(api_func, *args, **kwargs):
    """Generic function to handle API calls with retry logic and rate limiting"""
    for attempt in range(MAX_RETRIES):
        try:
            # Rate limiting
            time.sleep(API_RATE_LIMIT_DELAY)

            result = api_func(*args, **kwargs)

            if result.get('status') == 'success':
                return result
            else:
                error_msg = result.get('message', 'No message')
                if attempt < MAX_RETRIES - 1:
                    print(f"API call failed (attempt {attempt + 1}): {error_msg}. Retrying...")
                    time.sleep(RETRY_DELAY)
                else:
                    return result

        except Exception as e:
            if attempt < MAX_RETRIES - 1:
                print(f"Exception in API call (attempt {attempt + 1}): {str(e)}. Retrying...")
                time.sleep(RETRY_DELAY)
            else:
                return {'status': 'failure', 'message': str(e), 'data': {}}

    return {'status': 'failure', 'message': 'Max retries exceeded', 'data': {}}

def fetch_live_market_quote(stocks):
    """Fetch live market quotes for multiple stocks using Market Quote API"""
    try:
        # Prepare securities dictionary for Market Quote API
        securities = {}
        for stock in stocks:
            exchange = stock['exchange_segment']
            if exchange not in securities:
                securities[exchange] = []
            securities[exchange].append(stock['security_id'])

        # Fetch OHLC data (includes LTP)
        data = api_call_with_retry(dhan.ohlc_data, securities)

        if data['status'] == 'success':
            return data['data']
        else:
            log_event('MARKET_QUOTE', 'live', 'API_ERROR', data.get('message', 'No message'))
            return {}

    except Exception as e:
        log_event('MARKET_QUOTE', 'live', 'EXCEPTION', str(e))
        return {}

def fetch_intraday_data(stock, interval, from_dt, to_dt):
    """Enhanced intraday data fetching with retry logic"""
    try:
        data = api_call_with_retry(
            dhan.intraday_minute_data,
            security_id=stock['security_id'],
            exchange_segment=stock['exchange_segment'],
            instrument_type=stock['instrument_type'],
            interval=int(interval),  # Ensure interval is integer
            from_date=from_dt.strftime('%Y-%m-%d %H:%M:%S'),
            to_date=to_dt.strftime('%Y-%m-%d %H:%M:%S')
        )

        if data['status'] == 'success':
            df = pd.DataFrame(data['data'])
            if not df.empty:
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
            return df
        else:
            log_event(stock['name'], f"{interval}min", 'API_ERROR', data.get('message', 'No message'))
    except Exception as e:
        log_event(stock['name'], f"{interval}min", 'EXCEPTION', str(e))
    return pd.DataFrame()

def fetch_daily_data(stock, from_date, to_date):
    """Enhanced daily data fetching with retry logic"""
    try:
        data = api_call_with_retry(
            dhan.historical_daily_data,
            security_id=stock['security_id'],
            exchange_segment=stock['exchange_segment'],
            instrument_type=stock['instrument_type'],
            expiry_code=0,
            from_date=from_date.strftime('%Y-%m-%d'),
            to_date=to_date.strftime('%Y-%m-%d')
        )

        if data['status'] == 'success':
            df = pd.DataFrame(data['data'])
            if not df.empty:
                # Rename timestamp to startTime for consistency with the rest of the script
                if 'timestamp' in df.columns:
                    df['startTime'] = pd.to_datetime(df['timestamp'], unit='s')
                    df.drop('timestamp', axis=1, inplace=True)
            return df
        else:
            log_event(stock['name'], 'daily', 'API_ERROR', data.get('message', 'No message'))
    except Exception as e:
        log_event(stock['name'], 'daily', 'EXCEPTION', str(e))
    return pd.DataFrame()

def save_live_quote_data(stocks):
    """Save live market quotes to a separate file for real-time monitoring"""
    live_quotes = fetch_live_market_quote(stocks)

    if live_quotes:
        # Create a DataFrame from live quotes
        quote_data = []
        timestamp = datetime.datetime.now()

        for exchange, instruments in live_quotes.items():
            for security_id, quote in instruments.items():
                # Find stock name from our stocks list
                stock_name = next((s['name'] for s in stocks if s['security_id'] == security_id), security_id)

                quote_data.append({
                    'timestamp': timestamp,
                    'stock': stock_name,
                    'security_id': security_id,
                    'exchange': exchange,
                    'ltp': quote.get('LTP', 0),
                    'open': quote.get('open', 0),
                    'high': quote.get('high', 0),
                    'low': quote.get('low', 0),
                    'close': quote.get('close', 0),
                    'volume': quote.get('volume', 0)
                })

        if quote_data:
            df = pd.DataFrame(quote_data)
            live_file = 'stock_data/live_quotes.csv'

            # Append to existing file or create new one
            if os.path.exists(live_file):
                df.to_csv(live_file, mode='a', header=False, index=False)
            else:
                df.to_csv(live_file, mode='w', header=True, index=False)

            log_event('LIVE_QUOTES', 'all', 'UPDATE', f"Live quotes saved for {len(quote_data)} instruments")
            return True

    return False

# Update logic
def update_intraday_data(stock):
    for interval in timeframes:
        file = intraday_filename(stock, interval)
        from_dt = datetime.datetime.combine(today, market_open)
        to_dt = now

        if os.path.exists(file):
            existing = pd.read_csv(file)
            existing['timestamp'] = pd.to_datetime(existing['timestamp'])
            latest_time = existing['timestamp'].max()
            if latest_time.date() >= today:
                log_event(stock['name'], f"{interval}min", 'SKIP', 'Intraday data already up-to-date')
                continue
            from_dt = latest_time + datetime.timedelta(minutes=int(interval))

        df_new = fetch_intraday_data(stock, interval, from_dt, to_dt)
        if not df_new.empty:
            if os.path.exists(file):
                df_existing = pd.read_csv(file)
                df_combined = pd.concat([df_existing, df_new], ignore_index=True)
            else:
                df_combined = df_new
            df_combined.drop_duplicates(subset='timestamp', keep='last', inplace=True)
            df_combined.to_csv(file, index=False)
            log_event(stock['name'], f"{interval}min", 'UPDATE', f"Intraday data updated with {len(df_new)} new rows")

def update_daily_data(stock):
    file = daily_filename(stock)
    last_date = today - datetime.timedelta(days=1)

    if os.path.exists(file):
        existing = pd.read_csv(file)
        existing['startTime'] = pd.to_datetime(existing['startTime'])
        last_available = existing['startTime'].max().date()
        if last_available >= last_date:
            log_event(stock['name'], 'daily', 'SKIP', 'Daily data already up-to-date')
            return
        from_date = last_available + datetime.timedelta(days=1)
    else:
        from_date = last_date - datetime.timedelta(days=3)

    df_new = fetch_daily_data(stock, from_date, last_date)
    if not df_new.empty:
        if os.path.exists(file):
            df_existing = pd.read_csv(file)
            df_combined = pd.concat([df_existing, df_new], ignore_index=True)
        else:
            df_combined = df_new

        df_combined['startTime'] = pd.to_datetime(df_combined['startTime'])
        df_combined.drop_duplicates(subset='startTime', keep='last', inplace=True)
        df_combined.sort_values('startTime', inplace=True)
        df_combined = df_combined.tail(3)  # Keep only last 3 days
        df_combined.to_csv(file, index=False)
        log_event(stock['name'], 'daily', 'UPDATE', f"Daily data updated with {len(df_new)} new rows")

def is_market_open():
    """Check if market is currently open"""
    current_time = datetime.datetime.now().time()
    return market_open <= current_time <= market_close

def run_live_data_collection():
    """Run continuous live data collection during market hours"""
    print("Starting live data collection...")
    log_event('SYSTEM', 'live', 'START', 'Live data collection started')

    live_data_interval = 30  # Fetch live quotes every 30 seconds
    last_live_fetch = datetime.datetime.now() - datetime.timedelta(seconds=live_data_interval)

    while is_market_open() and is_trading_day(today):
        current_time = datetime.datetime.now()

        # Fetch live quotes at regular intervals
        if (current_time - last_live_fetch).seconds >= live_data_interval:
            print(f"Fetching live quotes at {current_time.strftime('%H:%M:%S')}")
            save_live_quote_data(stocks)
            last_live_fetch = current_time

        # Update intraday data every few minutes
        for stock in stocks:
            update_intraday_data(stock)

        # Wait before next iteration
        time.sleep(60)  # Wait 1 minute before next update

    log_event('SYSTEM', 'live', 'END', 'Live data collection ended')
    print("Live data collection ended.")

def validate_data_gaps():
    """Validate and report any gaps in the data"""
    print("Validating data for gaps...")

    for stock in stocks:
        for interval in timeframes:
            file = intraday_filename(stock, interval)
            if os.path.exists(file):
                df = pd.read_csv(file)
                if not df.empty:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    df = df.sort_values('timestamp')

                    # Check for gaps larger than the interval
                    time_diffs = df['timestamp'].diff()
                    expected_diff = datetime.timedelta(minutes=int(interval))
                    large_gaps = time_diffs[time_diffs > expected_diff * 2]  # Gaps larger than 2x interval

                    if not large_gaps.empty:
                        gap_count = len(large_gaps)
                        log_event(stock['name'], f"{interval}min", 'GAP_DETECTED',
                                f"{gap_count} gaps found in data")
                        print(f"⚠️  {stock['name']} {interval}min: {gap_count} data gaps detected")

# Enhanced main execution logic
def main():
    """Main execution function with enhanced live data capabilities"""
    print(f"DhanHQ Live Data Fetcher - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Market Hours: {market_open} - {market_close}")
    print(f"Trading Day: {'Yes' if is_trading_day(today) else 'No'}")
    print(f"Market Open: {'Yes' if is_market_open() else 'No'}")
    print("-" * 50)

    if is_trading_day(today):
        if is_market_open():
            print("🟢 Market is OPEN - Starting live data collection")

            # First, update any missing historical data
            print("Updating missing intraday data...")
            for stock in stocks:
                update_intraday_data(stock)

            # Then start live data collection
            run_live_data_collection()

        else:
            print("🔴 Market is CLOSED - Updating daily data")

            # Update daily data when market is closed
            for stock in stocks:
                update_daily_data(stock)

            # Also fetch final live quotes for the day
            print("Fetching final quotes for the day...")
            save_live_quote_data(stocks)
    else:
        print("📅 Non-trading day - Performing maintenance tasks")

        # On non-trading days, update daily data and validate
        for stock in stocks:
            update_daily_data(stock)
            log_event(stock['name'], 'all', 'SKIP', 'Non-trading day')

        # Validate data for gaps
        validate_data_gaps()

    print("\n📊 Data collection summary:")
    print(f"Stocks monitored: {len(stocks)}")
    print(f"Timeframes: {', '.join(timeframes)} minutes")
    print(f"Data directory: stock_data/")
    print(f"Logs directory: logs/")

if __name__ == "__main__":
    main()
