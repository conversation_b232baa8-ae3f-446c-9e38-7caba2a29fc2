#!/bin/sh
'''exec' '/Volumes/Kaundinya1TB/docs/Stocks and Shares/Testing_Dhan_API/.venv/bin/python3' "$0" "$@"
' '''
# -*- coding: utf-8 -*-
import sys
from charset_normalizer import cli
if __name__ == "__main__":
    if sys.argv[0].endswith("-script.pyw"):
        sys.argv[0] = sys.argv[0][:-11]
    elif sys.argv[0].endswith(".exe"):
        sys.argv[0] = sys.argv[0][:-4]
    sys.exit(cli.cli_detect())
