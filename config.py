"""
Configuration file for DhanHQ Live Data Fetcher
"""

# API Configuration
CLIENT_ID = "1105577608"
ACCESS_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

# Market Configuration
MARKET_OPEN_TIME = "09:15"  # Market opening time (24-hour format)
MARKET_CLOSE_TIME = "15:30"  # Market closing time (24-hour format)

# Data Collection Configuration
TIMEFRAMES = ['1', '5', '15', '25', '60']  # Intraday timeframes in minutes
LIVE_DATA_INTERVAL = 30  # Fetch live quotes every N seconds
INTRADAY_UPDATE_INTERVAL = 60  # Update intraday data every N seconds

# API Rate Limiting
API_RATE_LIMIT_DELAY = 0.2  # Delay between API calls (seconds)
MAX_RETRIES = 3  # Maximum number of retries for failed API calls
RETRY_DELAY = 1  # Delay between retries (seconds)

# File Paths
DATA_DIRECTORY = "stock_data"
LOGS_DIRECTORY = "logs"
LIVE_QUOTES_FILE = "live_quotes.csv"

# Stocks to monitor
STOCKS = [
    {'name': 'RELIANCE', 'security_id': '1333', 'exchange_segment': 'NSE_EQ', 'instrument_type': 'EQUITY'},
    {'name': 'TCS', 'security_id': '11536', 'exchange_segment': 'NSE_EQ', 'instrument_type': 'EQUITY'},
    {'name': 'INFY', 'security_id': '1594', 'exchange_segment': 'NSE_EQ', 'instrument_type': 'EQUITY'},
    {'name': 'HDFCBANK', 'security_id': '1330', 'exchange_segment': 'NSE_EQ', 'instrument_type': 'EQUITY'},
    {'name': 'ICICIBANK', 'security_id': '1334', 'exchange_segment': 'NSE_EQ', 'instrument_type': 'EQUITY'},
    {'name': 'NIFTY 50', 'security_id': '*********', 'exchange_segment': 'NSE_IDX', 'instrument_type': 'INDEX'}
]

# Logging Configuration
LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR
ENABLE_CONSOLE_LOGGING = True
ENABLE_FILE_LOGGING = True

# Data Validation
ENABLE_GAP_DETECTION = True
GAP_THRESHOLD_MULTIPLIER = 2  # Consider gaps larger than N times the interval as significant

# Advanced Features
ENABLE_LIVE_QUOTES = True  # Enable live quote collection
ENABLE_DATA_VALIDATION = True  # Enable data gap validation
KEEP_DAILY_DATA_DAYS = 3  # Number of days of daily data to keep
