#!/usr/bin/env python3
"""
Test script for enhanced DhanHQ API functionality
"""

import sys
import datetime
import pandas as pd
from dhanhq import dhanhq
import config

def test_api_connection():
    """Test basic API connection"""
    print("Testing API connection...")
    try:
        dhan = dhanhq(config.CLIENT_ID, config.ACCESS_TOKEN)
        
        # Test with a simple market quote call
        securities = {"NSE_EQ": ["1333"]}  # RELIANCE
        response = dhan.ohlc_data(securities)
        
        if response.get('status') == 'success':
            print("✅ API connection successful")
            print(f"Sample data: {response['data']}")
            return True
        else:
            print(f"❌ API connection failed: {response.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during API test: {str(e)}")
        return False

def test_market_quote_api():
    """Test Market Quote API with multiple stocks"""
    print("\nTesting Market Quote API...")
    try:
        dhan = dhanhq(config.CLIENT_ID, config.ACCESS_TOKEN)
        
        # Prepare securities for multiple stocks
        securities = {}
        for stock in config.STOCKS[:3]:  # Test with first 3 stocks
            exchange = stock['exchange_segment']
            if exchange not in securities:
                securities[exchange] = []
            securities[exchange].append(stock['security_id'])
        
        print(f"Testing with securities: {securities}")
        
        # Test OHLC data
        response = dhan.ohlc_data(securities)
        
        if response.get('status') == 'success':
            print("✅ Market Quote API successful")
            data = response['data']
            
            for exchange, instruments in data.items():
                print(f"\n{exchange}:")
                for security_id, quote in instruments.items():
                    stock_name = next((s['name'] for s in config.STOCKS if s['security_id'] == security_id), security_id)
                    print(f"  {stock_name}: LTP={quote.get('LTP', 'N/A')}, Volume={quote.get('volume', 'N/A')}")
            
            return True
        else:
            print(f"❌ Market Quote API failed: {response.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during Market Quote test: {str(e)}")
        return False

def test_historical_data_api():
    """Test Historical Data APIs"""
    print("\nTesting Historical Data APIs...")
    try:
        dhan = dhanhq(config.CLIENT_ID, config.ACCESS_TOKEN)
        
        # Test with RELIANCE
        test_stock = config.STOCKS[0]
        print(f"Testing with {test_stock['name']}")
        
        # Test daily data
        to_date = datetime.date.today() - datetime.timedelta(days=1)
        from_date = to_date - datetime.timedelta(days=2)
        
        print(f"Fetching daily data from {from_date} to {to_date}")
        daily_response = dhan.historical_daily_data(
            security_id=test_stock['security_id'],
            exchange_segment=test_stock['exchange_segment'],
            instrument_type=test_stock['instrument_type'],
            expiry_code=0,
            from_date=from_date.strftime('%Y-%m-%d'),
            to_date=to_date.strftime('%Y-%m-%d')
        )
        
        if daily_response.get('status') == 'success':
            print("✅ Daily Historical Data API successful")
            daily_data = daily_response['data']
            print(f"Daily data records: {len(daily_data.get('timestamp', []))}")
        else:
            print(f"❌ Daily Historical Data API failed: {daily_response.get('message', 'Unknown error')}")
        
        # Test intraday data (1-minute)
        from_dt = datetime.datetime.combine(to_date, datetime.time(9, 15))
        to_dt = datetime.datetime.combine(to_date, datetime.time(10, 0))
        
        print(f"Fetching 1-minute intraday data from {from_dt} to {to_dt}")
        intraday_response = dhan.intraday_minute_data(
            security_id=test_stock['security_id'],
            exchange_segment=test_stock['exchange_segment'],
            instrument_type=test_stock['instrument_type'],
            interval='1',
            from_date=from_dt.strftime('%Y-%m-%d %H:%M:%S'),
            to_date=to_dt.strftime('%Y-%m-%d %H:%M:%S')
        )
        
        if intraday_response.get('status') == 'success':
            print("✅ Intraday Historical Data API successful")
            intraday_data = intraday_response['data']
            print(f"Intraday data records: {len(intraday_data.get('timestamp', []))}")
            return True
        else:
            print(f"❌ Intraday Historical Data API failed: {intraday_response.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during Historical Data test: {str(e)}")
        return False

def test_data_processing():
    """Test data processing and DataFrame creation"""
    print("\nTesting data processing...")
    try:
        # Create sample data similar to API response
        sample_data = {
            'timestamp': [1748457000, 1748457060, 1748457120],
            'open': [1928.0, 1930.0, 1932.0],
            'high': [1940.0, 1942.0, 1944.0],
            'low': [1920.0, 1922.0, 1924.0],
            'close': [1935.0, 1937.0, 1939.0],
            'volume': [1000, 1100, 1200]
        }
        
        # Test DataFrame creation and timestamp conversion
        df = pd.DataFrame(sample_data)
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
        
        print("✅ DataFrame creation successful")
        print(f"Sample processed data:\n{df.head()}")
        
        # Test gap detection logic
        time_diffs = df['timestamp'].diff()
        expected_diff = datetime.timedelta(minutes=1)
        large_gaps = time_diffs[time_diffs > expected_diff * 2]
        
        print(f"Gap detection test: {len(large_gaps)} gaps found (expected: 0)")
        
        return True
        
    except Exception as e:
        print(f"❌ Exception during data processing test: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("DhanHQ Enhanced API Test Suite")
    print("=" * 50)
    
    tests = [
        ("API Connection", test_api_connection),
        ("Market Quote API", test_market_quote_api),
        ("Historical Data API", test_historical_data_api),
        ("Data Processing", test_data_processing)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! The enhanced API is ready to use.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the configuration and API credentials.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
