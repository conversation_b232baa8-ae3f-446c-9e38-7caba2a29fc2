# DhanHQ Live Data Fetcher - Enhanced Version

A comprehensive Python script for fetching live and historical stock market data using the DhanHQ API v2.2. This enhanced version includes live data collection, intelligent gap detection, retry logic, and comprehensive logging.

## 🚀 Features

### Core Functionality
- **Live Data Collection**: Real-time market quotes during trading hours
- **Historical Data Fetching**: Daily and intraday historical data
- **Intelligent Gap Detection**: Automatically detects and reports data gaps
- **Retry Logic**: Robust error handling with automatic retries
- **Rate Limiting**: Respects API rate limits to prevent throttling
- **Comprehensive Logging**: Detailed activity logs for monitoring

### Enhanced Capabilities
- **Market Hours Detection**: Automatically detects trading hours and market status
- **Live Quote Monitoring**: Continuous fetching of live market quotes
- **Data Validation**: Validates data integrity and reports issues
- **Flexible Configuration**: Easy-to-modify configuration file
- **Multiple Timeframes**: Supports 1, 5, 15, 25, and 60-minute intervals

## 📁 File Structure

```
Testing_Dhan_API/
├── TestDHanAPI.py          # Main enhanced script
├── config.py               # Configuration file
├── demo_live_data.py       # Demo script showcasing features
├── test_enhanced_api.py    # Test suite for validation
├── pyproject.toml          # UV project configuration
├── uv.lock                 # Dependency lock file
├── stock_data/             # Directory for stock data files
│   ├── *_daily.csv         # Daily historical data
│   ├── *intraday*min.csv   # Intraday data files
│   └── live_quotes.csv     # Live market quotes
└── logs/                   # Directory for log files
    └── activity_log.csv    # Activity and error logs
```

## 🛠 Installation

1. **Clone or download the files** to your local directory

2. **Install UV** (if not already installed):
   ```bash
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

3. **Create UV environment and install dependencies**:
   ```bash
   uv venv
   uv pip install dhanhq pandas
   ```

4. **Update configuration** in `config.py`:
   - Set your `CLIENT_ID` and `ACCESS_TOKEN`
   - Modify stock list as needed
   - Adjust timeframes and intervals

## 🔧 Configuration

Edit `config.py` to customize:

```python
# API Configuration
CLIENT_ID = "your_client_id"
ACCESS_TOKEN = "your_access_token"

# Market Configuration
MARKET_OPEN_TIME = "09:15"
MARKET_CLOSE_TIME = "15:30"

# Data Collection
TIMEFRAMES = ['1', '5', '15', '25', '60']
LIVE_DATA_INTERVAL = 30  # seconds

# Stocks to monitor
STOCKS = [
    {'name': 'RELIANCE', 'security_id': '1333', 'exchange_segment': 'NSE_EQ', 'instrument_type': 'EQUITY'},
    # Add more stocks...
]
```

## 🚀 Usage

### Basic Usage
```bash
# Activate UV environment
source .venv/bin/activate

# Run the main script
python TestDHanAPI.py
```

### Demo and Testing
```bash
# Run the demo to see features
python demo_live_data.py

# Run tests to validate setup
python test_enhanced_api.py
```

## 📊 How It Works

### Market Hours Detection
The script automatically detects:
- **Trading Days**: Monday to Friday (excludes weekends)
- **Market Hours**: 9:15 AM to 3:30 PM IST
- **Market Status**: Open/Closed based on current time

### Data Collection Logic

#### During Market Hours (Live Mode)
1. **Missing Data Update**: First updates any missing historical data
2. **Live Quote Collection**: Fetches live quotes every 30 seconds
3. **Intraday Updates**: Updates intraday data every minute
4. **Continuous Monitoring**: Runs until market closes

#### After Market Hours
1. **Daily Data Update**: Fetches daily historical data
2. **Final Quotes**: Captures final quotes for the day
3. **Data Validation**: Checks for gaps and inconsistencies

#### Non-Trading Days
1. **Maintenance Mode**: Updates daily data
2. **Gap Validation**: Comprehensive data gap analysis
3. **Cleanup**: Maintains data files and logs

### Data Gap Detection
The script automatically detects:
- **Missing Candles**: Gaps larger than 2x the timeframe interval
- **Data Inconsistencies**: Timestamp irregularities
- **API Failures**: Tracks and reports API errors

## 📈 Data Files

### Intraday Data Files
- **Format**: `{STOCK_NAME}intraday{INTERVAL}min.csv`
- **Example**: `RELIANCEintraday5min.csv`
- **Columns**: timestamp, open, high, low, close, volume

### Daily Data Files
- **Format**: `{STOCK_NAME}_daily.csv`
- **Example**: `RELIANCE_daily.csv`
- **Columns**: startTime, open, high, low, close, volume

### Live Quotes File
- **Format**: `live_quotes.csv`
- **Columns**: timestamp, stock, security_id, exchange, ltp, open, high, low, close, volume

## 📝 Logging

### Activity Log
- **File**: `logs/activity_log.csv`
- **Tracks**: All API calls, updates, errors, and system events
- **Format**: timestamp, stock, timeframe, event_type, message

### Event Types
- `UPDATE`: Successful data update
- `SKIP`: Operation skipped (already up-to-date)
- `API_ERROR`: API call failed
- `EXCEPTION`: System exception occurred
- `GAP_DETECTED`: Data gap found
- `START/END`: System start/stop events

## 🔄 API Features Used

### DhanHQ v2.2 APIs
- **Market Quote API**: Real-time OHLC data for multiple instruments
- **Historical Daily Data**: Daily candles with OHLC and volume
- **Intraday Minute Data**: Minute-level historical data (up to 5 years)
- **Enhanced Error Handling**: Comprehensive error messages

### Rate Limiting
- **API Delay**: 200ms between calls
- **Retry Logic**: Up to 3 retries with exponential backoff
- **Daily Limits**: Respects 100,000 requests/day limit

## 🛡 Error Handling

### Robust Error Management
- **API Failures**: Automatic retries with backoff
- **Network Issues**: Graceful handling of connectivity problems
- **Data Validation**: Checks for malformed responses
- **Logging**: All errors logged with context

### Common Issues and Solutions
1. **API Rate Limiting**: Automatic delays prevent throttling
2. **Invalid Security IDs**: Logged as API errors
3. **Market Closed**: Appropriate handling for non-trading periods
4. **Data Gaps**: Detected and reported for manual review

## 📋 Requirements

- Python 3.8+
- UV package manager
- DhanHQ API credentials
- Active internet connection
- Valid DhanHQ account with API access

## 🎯 Use Cases

### For Traders
- **Live Monitoring**: Real-time price tracking
- **Historical Analysis**: Backtesting strategies
- **Gap Analysis**: Identifying data quality issues

### For Developers
- **API Integration**: Example of robust API usage
- **Data Pipeline**: Foundation for trading systems
- **Error Handling**: Best practices for financial APIs

### For Analysts
- **Data Collection**: Automated historical data gathering
- **Quality Assurance**: Built-in data validation
- **Reporting**: Comprehensive activity logs

## 🔮 Future Enhancements

- **WebSocket Integration**: Real-time streaming data
- **Database Storage**: PostgreSQL/MongoDB integration
- **Advanced Analytics**: Technical indicators calculation
- **Alert System**: Price/volume threshold notifications
- **Web Dashboard**: Real-time monitoring interface

## 📞 Support

For issues related to:
- **DhanHQ API**: Check [DhanHQ Documentation](https://dhanhq.co/docs/v2/)
- **Script Issues**: Review logs in `logs/activity_log.csv`
- **Configuration**: Verify settings in `config.py`

## 📄 License

This project is for educational and personal use. Please ensure compliance with DhanHQ's terms of service and API usage guidelines.
